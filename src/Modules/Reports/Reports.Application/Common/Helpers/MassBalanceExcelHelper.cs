using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using Reports.Application.Common.Constants;
using Reports.Domain.ValueObjects;
using Reports.Domain.Entities;

namespace Reports.Application.Common.Helpers;

public static class MassBalanceExcelHelper
{
    public static byte[] GenerateExcel(MassBalance massBalance, string title, DateTime generationDate, string period, ILookup<string, string> areaCodeMappings)
    {
        var workbook = new XSSFWorkbook();
        var sheet = workbook.CreateSheet(MassBalanceExcelConstants.MainTitle);

        var currentRow = (int)ColumnIndex.First;

        currentRow = CreateHeader(sheet, currentRow, title, generationDate, period);

        var balanceSheetRows = massBalance.GetBalanceSheetRows(areaCodeMappings);
        currentRow = CreateBalanceSheetTable(sheet, currentRow, balanceSheetRows);

        currentRow = CreateSummaryTable(sheet, currentRow, massBalance.Weighins, massBalance.FinalDisposition.Totals);

        _ = CreateDistributionTable(sheet, currentRow, massBalance.Distributions);

        ApplyWorkbookFormatting(sheet);

        SetColumnWidths(sheet);

        using var stream = new MemoryStream();
        workbook.Write(stream);
        return stream.ToArray();
    }

    private static int CreateHeader(ISheet sheet, int startRow, string title, DateTime generationDate, string period)
    {
        var currentRow = startRow;
        var balanceSheetConfig = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);

        var titleRow = sheet.CreateRow(currentRow++);
        var titleCell = titleRow.CreateCell((int)ColumnIndex.First);
        titleCell.SetCellValue(title);

        var titleMergeRegion = new CellRangeAddress(startRow, startRow, (int)ColumnIndex.First, balanceSheetConfig.ColumnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset);
        sheet.AddMergedRegion(titleMergeRegion);
        CreateCellsInRange(titleRow, (int)ColumnIndex.Second, balanceSheetConfig.ColumnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset);

        var dateRow = sheet.CreateRow(currentRow++);
        var dateCell = dateRow.CreateCell((int)ColumnIndex.First);
        dateCell.SetCellValue($"{MassBalanceExcelConstants.Text.GeneratedPrefix}{generationDate.ToString(MassBalanceExcelConstants.Dates.HeaderFormat)}");

        var periodRow = sheet.CreateRow(currentRow++);
        var periodCell = periodRow.CreateCell((int)ColumnIndex.First);
        periodCell.SetCellValue($"{MassBalanceExcelConstants.Text.PeriodPrefix}{period}");

        sheet.CreateRow(currentRow++);

        return currentRow;
    }

    private static int CreateBalanceSheetTable(ISheet sheet, int startRow, List<BalanceSheetRow> data)
    {
        var currentRow = startRow;
        var config = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);

        currentRow = CreateTableHeader(sheet, currentRow, config.Title, config.ColumnCount);

        currentRow = CreateBalanceSheetMultiRowHeaders(sheet, currentRow);

        var dataStartRow = currentRow;
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow);
            PopulateBalanceSheetDataRow(dataRow, row, currentRow + 1);
            currentRow++;
        }

        currentRow = CreateBalanceSheetTotalsRow(sheet, currentRow, dataStartRow + 1, currentRow);

        AddSectionSpacing(sheet, ref currentRow);

        return currentRow;
    }

    private static int CreateSummaryTable(ISheet sheet, int startRow, WeighinTonsResume weighins, WeighinTonsResume finalDispositionTotals)
    {
        var currentRow = startRow;
        var config = MassBalanceExcelConstants.GetTableConfig(TableType.Summary);

        currentRow = CreateTableHeader(sheet, currentRow, config.Title, config.ColumnCount);

        currentRow = CreateColumnHeaders(sheet, currentRow, MassBalanceExcelConstants.SummaryHeaders.Values.ToArray());

        currentRow = CreateSummaryDataRows(sheet, currentRow, weighins, finalDispositionTotals);

        AddSectionSpacing(sheet, ref currentRow);

        return currentRow;
    }

    private static int CreateDistributionTable(ISheet sheet, int startRow, List<Distribution> data)
    {
        var currentRow = startRow;
        var config = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);

        currentRow = CreateTableHeader(sheet, currentRow, config.Title, config.ColumnCount);

        currentRow = CreateColumnHeaders(sheet, currentRow, MassBalanceExcelConstants.DistributionHeaders.Values.ToArray());

        var dataStartRow = currentRow;
        foreach (var row in data)
        {
            var dataRow = sheet.CreateRow(currentRow++);
            PopulateDistributionDataRow(dataRow, row);
        }

        currentRow = CreateDistributionTotalsRow(sheet, currentRow, dataStartRow + 1, currentRow);

        return currentRow;
    }

    private static int CreateTableHeader(ISheet sheet, int startRow, string title, int columnCount)
    {
        var headerRow = sheet.CreateRow(startRow);
        var headerCell = headerRow.CreateCell((int)ColumnIndex.First);
        headerCell.SetCellValue(title);

        var mergeRegion = new CellRangeAddress(startRow, startRow, (int)ColumnIndex.First, columnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset);
        sheet.AddMergedRegion(mergeRegion);
        CreateCellsInRange(headerRow, (int)ColumnIndex.Second, columnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset);

        return startRow + MassBalanceExcelConstants.Layout.HeaderRowOffset;
    }

    private static int CreateColumnHeaders(ISheet sheet, int startRow, string[] headers)
    {
        var headerRow = sheet.CreateRow(startRow);
        for (int i = 0; i < headers.Length; i++)
        {
            var cell = headerRow.CreateCell(i);
            cell.SetCellValue(headers[i]);
        }
        return startRow + MassBalanceExcelConstants.Layout.HeaderRowOffset;
    }

    private static int CreateBalanceSheetMultiRowHeaders(ISheet sheet, int startRow)
    {
        var currentRow = startRow;
        var config = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);

        var firstHeaderRow = sheet.CreateRow(currentRow);
        for (int i = 0; i < config.ColumnCount; i++)
        {
            firstHeaderRow.CreateCell(i);
        }

        var areaCell = firstHeaderRow.GetCell((int)BalanceSheetColumn.AreaCode);
        areaCell.SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.AreaCode));
        var areaMergeRegion = new CellRangeAddress(currentRow, currentRow + MassBalanceExcelConstants.Layout.MergeRegionRowOffset, (int)BalanceSheetColumn.AreaCode, (int)BalanceSheetColumn.AreaCode);
        sheet.AddMergedRegion(areaMergeRegion);

        var nameCell = firstHeaderRow.GetCell((int)BalanceSheetColumn.AreaName);
        nameCell.SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.AreaName));
        var nameMergeRegion = new CellRangeAddress(currentRow, currentRow + MassBalanceExcelConstants.Layout.MergeRegionRowOffset, (int)BalanceSheetColumn.AreaName, (int)BalanceSheetColumn.AreaName);
        sheet.AddMergedRegion(nameMergeRegion);

        var f14Cell = firstHeaderRow.GetCell((int)BalanceSheetColumn.UrbanCleaning);
        f14Cell.SetCellValue(MassBalanceExcelConstants.GetSubHeader(BalanceSheetColumn.UrbanCleaning));
        var f14MergeRegion = new CellRangeAddress(currentRow, currentRow, (int)BalanceSheetColumn.UrbanCleaning, (int)BalanceSheetColumn.TotalF14);
        sheet.AddMergedRegion(f14MergeRegion);

        var f34Cell = firstHeaderRow.GetCell((int)BalanceSheetColumn.TotalByNuap);
        f34Cell.SetCellValue(MassBalanceExcelConstants.GetSubHeader(BalanceSheetColumn.TotalByNuap));
        var f34MergeRegion = new CellRangeAddress(currentRow, currentRow, (int)BalanceSheetColumn.TotalByNuap, (int)BalanceSheetColumn.Difference);
        sheet.AddMergedRegion(f34MergeRegion);

        currentRow++;

        var secondHeaderRow = sheet.CreateRow(currentRow);
        for (int i = 0; i < config.ColumnCount; i++)
        {
            secondHeaderRow.CreateCell(i);
        }

        secondHeaderRow.GetCell((int)BalanceSheetColumn.UrbanCleaning).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.UrbanCleaning));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.Sweeping).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.Sweeping));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.NonRecyclable).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.NonRecyclable));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.Rejection).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.Rejection));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.Recyclable).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.Recyclable));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.TotalF14).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.TotalF14));

        secondHeaderRow.GetCell((int)BalanceSheetColumn.TotalByNuap).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.TotalByNuap));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.Discounts).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.Discounts));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.TotalF34).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.TotalF34));
        secondHeaderRow.GetCell((int)BalanceSheetColumn.Difference).SetCellValue(MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.Difference));

        return currentRow + MassBalanceExcelConstants.Layout.HeaderRowOffset;
    }

    private static void PopulateBalanceSheetDataRow(IRow dataRow, BalanceSheetRow row, int rowNumber)
    {
        dataRow.CreateCell((int)BalanceSheetColumn.AreaCode).SetCellValue(row.AreaCode);
        dataRow.CreateCell((int)BalanceSheetColumn.AreaName).SetCellValue(row.AreaName);
        dataRow.CreateCell((int)BalanceSheetColumn.UrbanCleaning).SetCellValue((double)row.UrbanCleaningTons);
        dataRow.CreateCell((int)BalanceSheetColumn.Sweeping).SetCellValue((double)row.SweepingTons);
        dataRow.CreateCell((int)BalanceSheetColumn.NonRecyclable).SetCellValue((double)row.NonRecyclableTons);
        dataRow.CreateCell((int)BalanceSheetColumn.Rejection).SetCellValue((double)row.RejectionTons);
        dataRow.CreateCell((int)BalanceSheetColumn.Recyclable).SetCellValue((double)row.RecyclableTons);

        var totalF14Cell = dataRow.CreateCell((int)BalanceSheetColumn.TotalF14);
        totalF14Cell.SetCellFormula($"SUM({MassBalanceExcelConstants.GetColumnLetter(BalanceSheetColumn.Sweeping)}{rowNumber},{MassBalanceExcelConstants.GetColumnLetter(BalanceSheetColumn.NonRecyclable)}{rowNumber})");

        dataRow.CreateCell((int)BalanceSheetColumn.TotalByNuap).SetCellValue((double)row.TotalByNUAP);
        dataRow.CreateCell((int)BalanceSheetColumn.Discounts).SetCellValue((double)row.Discounts);

        var totalF34Cell = dataRow.CreateCell((int)BalanceSheetColumn.TotalF34);
        totalF34Cell.SetCellFormula($"{MassBalanceExcelConstants.GetColumnLetter(BalanceSheetColumn.TotalByNuap)}{rowNumber}-{MassBalanceExcelConstants.GetColumnLetter(BalanceSheetColumn.Discounts)}{rowNumber}");

        var differenceCell = dataRow.CreateCell((int)BalanceSheetColumn.Difference);
        if (row.AreaName == MassBalanceExcelConstants.Text.OtrosAreaName)
        {
            differenceCell.SetCellValue(MassBalanceExcelConstants.Text.ZeroValue);
        }
        else
        {
            differenceCell.SetCellFormula($"{MassBalanceExcelConstants.GetColumnLetter(BalanceSheetColumn.TotalF34)}{rowNumber}-{MassBalanceExcelConstants.GetColumnLetter(BalanceSheetColumn.TotalF14)}{rowNumber}+{MassBalanceExcelConstants.GetColumnLetter(BalanceSheetColumn.Discounts)}{rowNumber}");
        }
    }

    private static void PopulateDistributionDataRow(IRow dataRow, Distribution row)
    {
        dataRow.CreateCell((int)DistributionColumn.RecyclingArea).SetCellValue(row.RecyclingArea);
        dataRow.CreateCell((int)DistributionColumn.ReportedTons).SetCellValue((double)row.ReportedTons);
        dataRow.CreateCell((int)DistributionColumn.Trips).SetCellValue(row.Trips);
        dataRow.CreateCell((int)DistributionColumn.CalculatedDistributedTons).SetCellValue((double)row.DistributedTons);
        dataRow.CreateCell((int)DistributionColumn.TollSharedRouteTons).SetCellValue((double)row.TollSharedRouteTons);
        dataRow.CreateCell((int)DistributionColumn.DistributionTollPercentage).SetCellValue((double)row.DistributionTollPercentage);
        dataRow.CreateCell((int)DistributionColumn.DeviationTons).SetCellValue((double)row.DeviationTons);
    }

    private static int CreateSummaryDataRows(ISheet sheet, int startRow, WeighinTonsResume weighins, WeighinTonsResume finalDispositionTotals)
    {
        var currentRow = startRow;

        var dataRow = sheet.CreateRow(currentRow++);
        dataRow.CreateCell((int)SummaryColumn.TotalDispositionFinal).SetCellValue((double)finalDispositionTotals.Total);
        dataRow.CreateCell((int)SummaryColumn.TotalEmvarias).SetCellValue((double)finalDispositionTotals.Emvarias);

        return currentRow;
    }

    private static void CreateCellsInRange(IRow row, int startCol, int endCol)
    {
        for (int col = startCol; col <= endCol; col++)
        {
            row.CreateCell(col);
        }
    }

    private static void AddSectionSpacing(ISheet sheet, ref int currentRow)
    {
        for (int i = 0; i < MassBalanceExcelConstants.Layout.SectionSpacingRows; i++)
        {
            sheet.CreateRow(currentRow++);
        }
    }

    private static string GetColumnLetter(int columnIndex)
    {
        if (columnIndex < MassBalanceExcelConstants.ColumnLetters.Threshold)
            return MassBalanceExcelConstants.ColumnLetters.Letters[columnIndex].ToString();

        return MassBalanceExcelConstants.ColumnLetters.Letters[columnIndex / MassBalanceExcelConstants.ColumnLetters.Threshold - MassBalanceExcelConstants.ColumnLetters.Offset].ToString() +
               MassBalanceExcelConstants.ColumnLetters.Letters[columnIndex % MassBalanceExcelConstants.ColumnLetters.Threshold].ToString();
    }

    private static int CreateBalanceSheetTotalsRow(ISheet sheet, int currentRow, int dataStartRow, int dataEndRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        var config = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);
        totalsRow.CreateCell((int)ColumnIndex.Second).SetCellValue(config.TotalLabel);

        for (int col = config.DataStartColumnIndex; col <= config.ColumnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset; col++)
        {
            var totalCell = totalsRow.CreateCell(col);
            totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
        }

        return currentRow;
    }

    private static int CreateDistributionTotalsRow(ISheet sheet, int currentRow, int dataStartRow, int dataEndRow)
    {
        var totalsRow = sheet.CreateRow(currentRow++);
        var config = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
        totalsRow.CreateCell((int)ColumnIndex.First).SetCellValue(config.TotalLabel);

        for (int col = config.DataStartColumnIndex; col <= config.ColumnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset; col++)
        {
            var totalCell = totalsRow.CreateCell(col);

            if (col == (int)DistributionColumn.CalculatedDistributedTons)
            {
                totalCell.SetCellFormula($"SUMPRODUCT({MassBalanceExcelConstants.GetColumnLetter(DistributionColumn.Trips)}{dataStartRow}:{MassBalanceExcelConstants.GetColumnLetter(DistributionColumn.Trips)}{dataEndRow},{MassBalanceExcelConstants.GetColumnLetter(DistributionColumn.CalculatedDistributedTons)}{dataStartRow}:{MassBalanceExcelConstants.GetColumnLetter(DistributionColumn.CalculatedDistributedTons)}{dataEndRow})");
            }
            else if (col == (int)DistributionColumn.TollSharedRouteTons)
            {
                totalCell.SetCellValue(MassBalanceExcelConstants.Text.DashValue);
            }
            else
            {
                totalCell.SetCellFormula($"SUM({GetColumnLetter(col)}{dataStartRow}:{GetColumnLetter(col)}{dataEndRow})");
            }
        }

        return currentRow;
    }

    private static void SetColumnWidths(ISheet sheet)
    {
        for (int i = 0; i < MassBalanceExcelConstants.Layout.AutoSizeColumnCount; i++)
        {
            sheet.AutoSizeColumn(i);
        }

        sheet.SetColumnWidth((int)ColumnIndex.First, MassBalanceExcelConstants.Widths.TimestampColumn);
        sheet.SetColumnWidth((int)BalanceSheetColumn.TotalByNuap, MassBalanceExcelConstants.Widths.TotalByNuapColumn);
    }

    private static void ApplyWorkbookFormatting(ISheet sheet)
    {
        var workbook = sheet.Workbook;
        var styles = CreateAllStyles(workbook);

        ApplyTitleFormatting(sheet, styles.TitleStyle);
        ApplyHeaderFormatting(sheet, styles.HeaderStyle);
        ApplyDataFormatting(sheet, styles);
        ApplyTotalFormatting(sheet, styles.TotalStyle);
        ApplyDistributionTableBorders(sheet, workbook);
        ApplyBordersToMergedRegions(sheet);
    }

    private static ExcelStyles CreateAllStyles(IWorkbook workbook)
    {
        return new ExcelStyles
        {
            TitleStyle = CreateTitleStyle(workbook),
            HeaderStyle = CreateHeaderStyle(workbook),
            DataStyle = CreateDataStyle(workbook),
            IntegerStyle = CreateIntegerStyle(workbook),
            CalculatedStyle = CreateCalculatedStyle(workbook),
            CompensationStyle = CreateCompensationStyle(workbook),
            TotalStyle = CreateTotalStyle(workbook)
        };
    }

    private static ICellStyle CreateTitleStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.FontHeightInPoints = MassBalanceExcelConstants.Formatting.TitleFontSize;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        return style;
    }

    private static ICellStyle CreateHeaderStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        font.Color = IndexedColors.White.Index;
        style.SetFont(font);
        style.Alignment = HorizontalAlignment.Center;
        style.FillForegroundColor = IndexedColors.Violet.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static ICellStyle CreateDataStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.DecimalFormat);
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    private static ICellStyle CreateIntegerStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.IntegerFormat);
        style.BorderTop = BorderStyle.Thin;
        style.BorderBottom = BorderStyle.Thin;
        style.BorderLeft = BorderStyle.Thin;
        style.BorderRight = BorderStyle.Thin;
        return style;
    }

    private static ICellStyle CreateCalculatedStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.DecimalFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static ICellStyle CreateCompensationStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.CompensationFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static ICellStyle CreateTotalStyle(IWorkbook workbook)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.DecimalFormat);
        style.FillForegroundColor = IndexedColors.Grey25Percent.Index;
        style.FillPattern = FillPattern.SolidForeground;
        style.BorderTop = BorderStyle.Thick;
        style.BorderBottom = BorderStyle.Thick;
        style.BorderLeft = BorderStyle.Thick;
        style.BorderRight = BorderStyle.Thick;
        return style;
    }

    private static void ApplyTitleFormatting(ISheet sheet, ICellStyle titleStyle)
    {
        for (int row = 0; row < MassBalanceExcelConstants.Layout.TitleRowSpan; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                var config = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);
                for (int col = 0; col < config.ColumnCount; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        cell.CellStyle = titleStyle;
                    }
                }
            }
        }
    }

    private static void ApplyHeaderFormatting(ISheet sheet, ICellStyle headerStyle)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                if (IsSubHeaderRow(row))
                {
                    var config = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);
                    ApplyStyleToRowRange(sheet, rowIndex, headerStyle, (int)ColumnIndex.First, config.ColumnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset);
                    continue;
                }

                var firstCell = row.GetCell((int)ColumnIndex.First);
                if (firstCell != null && firstCell.CellType == CellType.String)
                {
                    var cellValue = firstCell.StringCellValue;
                    var columnCount = GetColumnCountForHeader(cellValue);

                    if (columnCount > (int)ColumnIndex.First)
                    {
                        ApplyStyleToRowRange(sheet, rowIndex, headerStyle, (int)ColumnIndex.First, columnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset);
                    }
                }
            }
        }
    }

    private static void ApplyDataFormatting(ISheet sheet, ExcelStyles styles)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsDataRow(row))
            {
                ApplyDataRowFormatting(row, styles);
            }
        }
    }

    private static void ApplyTotalFormatting(ISheet sheet, ICellStyle totalStyle)
    {
        for (int rowIndex = 0; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null && IsTotalRow(row))
            {
                var columnCount = GetColumnCountForTotalRow(sheet, rowIndex);
                ApplyStyleToRowRange(sheet, rowIndex, totalStyle, (int)ColumnIndex.First, columnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset);
            }
        }
    }

    private static void ApplyDistributionTableBorders(ISheet sheet, IWorkbook workbook)
    {
        var (startRow, endRow) = FindDistributionTableBounds(sheet);
        if (startRow == MassBalanceExcelConstants.Layout.NotFoundIndex || endRow == MassBalanceExcelConstants.Layout.NotFoundIndex) return;

        for (int rowIndex = startRow; rowIndex <= endRow; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                ApplyDistributionRowBorders(row, workbook, rowIndex, startRow, endRow);
            }
        }
    }

    private static void ApplyBordersToMergedRegions(ISheet sheet)
    {
        var workbook = sheet.Workbook;

        for (int i = 0; i < sheet.NumMergedRegions; i++)
        {
            var mergedRegion = sheet.GetMergedRegion(i);
            ApplyBordersToMergedRegion(sheet, workbook, mergedRegion);
        }
    }

    private static void ApplyStyleToRowRange(ISheet sheet, int rowIndex, ICellStyle style, int startCol, int endCol)
    {
        var row = sheet.GetRow(rowIndex);
        if (row != null)
        {
            for (int col = startCol; col <= endCol; col++)
            {
                var cell = row.GetCell(col);
                if (cell != null)
                {
                    cell.CellStyle = style;
                }
            }
        }
    }

    private static int GetColumnCountForHeader(string cellValue)
    {
        var balanceSheetConfig = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);
        var summaryConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Summary);
        var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);

        return cellValue switch
        {
            var value when value.Contains(balanceSheetConfig.Title) => balanceSheetConfig.ColumnCount,
            var value when value.Contains(summaryConfig.Title) => summaryConfig.ColumnCount,
            var value when value.Contains(MassBalanceExcelConstants.Search.Concept) => summaryConfig.ColumnCount,
            var value when value.Contains(distributionConfig.Title) => distributionConfig.ColumnCount,
            var value when value.Contains(MassBalanceExcelConstants.Search.RecyclingArea) => distributionConfig.ColumnCount,
            var value when value == MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.AreaCode) => balanceSheetConfig.ColumnCount,
            var value when value == MassBalanceExcelConstants.GetHeader(SummaryColumn.TotalDispositionFinal) => summaryConfig.ColumnCount,
            var value when value == MassBalanceExcelConstants.GetHeader(DistributionColumn.RecyclingArea) => distributionConfig.ColumnCount,
            _ => 0
        };
    }

    private static bool IsSubHeaderRow(IRow row)
    {
        var f14Cell = row.GetCell((int)BalanceSheetColumn.UrbanCleaning);
        if (f14Cell != null && f14Cell.CellType == CellType.String && f14Cell.StringCellValue.Contains(MassBalanceExcelConstants.GetSubHeader(BalanceSheetColumn.UrbanCleaning)))
        {
            return true;
        }

        var f34Cell = row.GetCell((int)BalanceSheetColumn.TotalByNuap);
        if (f34Cell != null && f34Cell.CellType == CellType.String && f34Cell.StringCellValue.Contains(MassBalanceExcelConstants.GetSubHeader(BalanceSheetColumn.TotalByNuap)))
        {
            return true;
        }

        var detailCell = row.GetCell((int)BalanceSheetColumn.UrbanCleaning);
        if (detailCell != null && detailCell.CellType == CellType.String && detailCell.StringCellValue == MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.UrbanCleaning))
        {
            return true;
        }

        var areaCell = row.GetCell((int)BalanceSheetColumn.AreaCode);
        if (areaCell != null && areaCell.CellType == CellType.String && areaCell.StringCellValue == MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.AreaCode))
        {
            return true;
        }

        return false;
    }

    private static bool IsDataRow(IRow row)
    {
        var firstCell = row.GetCell((int)ColumnIndex.First) ?? row.GetCell((int)ColumnIndex.Second);
        var cellValue = MassBalanceExcelConstants.Text.EmptyString;

        if (firstCell != null && firstCell.CellType == CellType.String)
        {
            cellValue = firstCell.StringCellValue;
        }

        if (cellValue.Contains(MassBalanceExcelConstants.Search.Total) || cellValue.Contains(MassBalanceExcelConstants.Search.Sum))
            return false;

        var summaryConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Summary);
        var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
        var balanceSheetConfig = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);

        if (cellValue.Contains(summaryConfig.Title) || cellValue.Contains(distributionConfig.Title) ||
            cellValue.Contains(balanceSheetConfig.Title))
            return false;

        if (cellValue == MassBalanceExcelConstants.Search.Concept || cellValue == MassBalanceExcelConstants.Search.RecyclingArea ||
            cellValue == MassBalanceExcelConstants.GetHeader(BalanceSheetColumn.AreaCode) || cellValue == MassBalanceExcelConstants.GetHeader(SummaryColumn.TotalDispositionFinal) ||
            cellValue == MassBalanceExcelConstants.GetHeader(DistributionColumn.RecyclingArea))
            return false;

        if (string.IsNullOrWhiteSpace(cellValue))
            return false;

        return true;
    }

    private static bool IsTotalRow(IRow row)
    {
        var firstCell = row.GetCell((int)ColumnIndex.First) ?? row.GetCell((int)ColumnIndex.Second);
        return firstCell != null && firstCell.CellType == CellType.String &&
               (firstCell.StringCellValue.Contains(MassBalanceExcelConstants.Search.Total) || firstCell.StringCellValue.Contains(MassBalanceExcelConstants.Search.Sum));
    }

    private static void ApplyDataRowFormatting(IRow row, ExcelStyles styles)
    {
        if (IsBalanceSheetDataRow(row))
        {
            ApplyBalanceSheetRowFormatting(row, styles);
        }
        else if (IsDistributionDataRow(row))
        {
            ApplyDistributionRowFormatting(row, styles);
        }
    }

    private static bool IsBalanceSheetDataRow(IRow row)
    {
        var balanceSheetConfig = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);
        return row.LastCellNum >= balanceSheetConfig.ColumnCount;
    }

    private static bool IsDistributionDataRow(IRow row)
    {
        var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
        return row.LastCellNum <= distributionConfig.ColumnCount && row.LastCellNum > 0;
    }

    private static void ApplyBalanceSheetRowFormatting(IRow row, ExcelStyles styles)
    {
        var balanceSheetConfig = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);
        for (int col = 0; col < balanceSheetConfig.ColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                if (balanceSheetConfig.FormulaColumns.Contains(col))
                {
                    cell.CellStyle = styles.CalculatedStyle;
                }
                else if (col >= balanceSheetConfig.DataStartColumnIndex || (col <= (int)ColumnIndex.Second && cell.CellType != CellType.Blank))
                {
                    cell.CellStyle = styles.DataStyle;
                }
            }
        }
    }

    private static void ApplyDistributionRowFormatting(IRow row, ExcelStyles styles)
    {
        var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
        for (int col = 0; col < distributionConfig.ColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                if (distributionConfig.CompensationColumns.Contains(col))
                {
                    cell.CellStyle = styles.CompensationStyle;
                }
                else if (distributionConfig.IntegerColumns.Contains(col))
                {
                    cell.CellStyle = styles.IntegerStyle;
                }
                else
                {
                    cell.CellStyle = styles.DataStyle;
                }
            }
        }
    }

    private static int GetColumnCountForTotalRow(ISheet sheet, int rowIndex)
    {
        var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
        var balanceSheetConfig = MassBalanceExcelConstants.GetTableConfig(TableType.BalanceSheet);

        for (int checkRow = rowIndex - MassBalanceExcelConstants.Layout.HeaderRowOffset; checkRow >= (int)ColumnIndex.First; checkRow--)
        {
            var checkRowObj = sheet.GetRow(checkRow);
            if (checkRowObj != null)
            {
                var checkCell = checkRowObj.GetCell((int)ColumnIndex.First);
                if (checkCell != null && checkCell.CellType == CellType.String)
                {
                    var cellValue = checkCell.StringCellValue;
                    if (cellValue == distributionConfig.Title)
                    {
                        return distributionConfig.ColumnCount;
                    }
                    else if (cellValue.Contains(balanceSheetConfig.Title))
                    {
                        return balanceSheetConfig.ColumnCount;
                    }
                }
            }
        }
        return balanceSheetConfig.ColumnCount;
    }

    private static (int startRow, int endRow) FindDistributionTableBounds(ISheet sheet)
    {
        int startRow = MassBalanceExcelConstants.Layout.NotFoundIndex, endRow = MassBalanceExcelConstants.Layout.NotFoundIndex;
        var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);

        for (int rowIndex = (int)ColumnIndex.First; rowIndex <= sheet.LastRowNum; rowIndex++)
        {
            var row = sheet.GetRow(rowIndex);
            if (row != null)
            {
                var firstCell = row.GetCell((int)ColumnIndex.First);
                if (firstCell != null && firstCell.CellType == CellType.String)
                {
                    var cellValue = firstCell.StringCellValue;
                    if (cellValue == distributionConfig.Title)
                    {
                        startRow = rowIndex;
                    }
                    else if (startRow != MassBalanceExcelConstants.Layout.NotFoundIndex && cellValue == distributionConfig.TotalLabel)
                    {
                        endRow = rowIndex;
                        break;
                    }
                }
            }
        }

        return (startRow, endRow);
    }

    private static void ApplyDistributionRowBorders(IRow row, IWorkbook workbook, int rowIndex, int startRow, int endRow)
    {
        bool isTopRow = rowIndex == startRow;
        bool isBottomRow = rowIndex == endRow;
        bool isHeaderRow = IsDistributionHeaderRow(row);
        var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);

        for (int col = 0; col < distributionConfig.ColumnCount; col++)
        {
            var cell = row.GetCell(col);
            if (cell != null)
            {
                bool isLeftColumn = col == (int)ColumnIndex.First;
                bool isRightColumn = col == distributionConfig.ColumnCount - MassBalanceExcelConstants.Layout.HeaderRowOffset;

                var borderStyle = CreateDistributionBorderStyle(workbook, col, isHeaderRow, isBottomRow, isTopRow, isLeftColumn, isRightColumn);
                cell.CellStyle = borderStyle;
            }
        }
    }

    private static bool IsDistributionHeaderRow(IRow row)
    {
        var firstCell = row.GetCell((int)ColumnIndex.First);
        if (firstCell != null && firstCell.CellType == CellType.String)
        {
            var cellValue = firstCell.StringCellValue;
            var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
            return cellValue == distributionConfig.Title || cellValue == MassBalanceExcelConstants.Search.RecyclingArea;
        }
        return false;
    }

    private static ICellStyle CreateDistributionBorderStyle(IWorkbook workbook, int col, bool isHeaderRow, bool isBottomRow, bool isTopRow, bool isLeftColumn, bool isRightColumn)
    {
        ICellStyle borderStyle;

        if (isHeaderRow)
        {
            borderStyle = workbook.CreateCellStyle();
            borderStyle.CloneStyleFrom(CreateHeaderStyle(workbook));
            borderStyle.BorderTop = BorderStyle.Thick;
            borderStyle.BorderBottom = BorderStyle.Thick;
            borderStyle.BorderLeft = BorderStyle.Thick;
            borderStyle.BorderRight = BorderStyle.Thick;
        }
        else if (isBottomRow)
        {
            var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
            if (distributionConfig.IntegerColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, false, false, true, false, false);
            }
            else if (distributionConfig.CompensationColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, false, false, true, false, false);
            }
            else
            {
                borderStyle = CreateDistributionTableBorderStyle(workbook, false, false, true, false, false);
            }

            borderStyle.BorderTop = BorderStyle.Thick;
            borderStyle.BorderBottom = BorderStyle.Thick;
            borderStyle.BorderLeft = BorderStyle.Thick;
            borderStyle.BorderRight = BorderStyle.Thick;
        }
        else
        {
            var distributionConfig = MassBalanceExcelConstants.GetTableConfig(TableType.Distribution);
            if (distributionConfig.IntegerColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableIntegerBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
            else if (distributionConfig.CompensationColumns.Contains(col))
            {
                borderStyle = CreateDistributionTableCompensationBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
            else
            {
                borderStyle = CreateDistributionTableBorderStyle(workbook, true, isTopRow, isBottomRow, isLeftColumn, isRightColumn);
            }
        }

        return borderStyle;
    }

    private static ICellStyle CreateDistributionTableBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.DecimalFormat);

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private static ICellStyle CreateDistributionTableIntegerBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.IntegerFormat);

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private static ICellStyle CreateDistributionTableCompensationBorderStyle(IWorkbook workbook, bool isOuterBorder = false, bool isTopRow = false, bool isBottomRow = false, bool isLeftColumn = false, bool isRightColumn = false)
    {
        var style = workbook.CreateCellStyle();
        var font = workbook.CreateFont();
        font.IsBold = true;
        style.SetFont(font);
        style.DataFormat = workbook.CreateDataFormat().GetFormat(MassBalanceExcelConstants.Formatting.CompensationFormat);
        style.FillForegroundColor = IndexedColors.LightYellow.Index;
        style.FillPattern = FillPattern.SolidForeground;

        style.BorderTop = (isOuterBorder && isTopRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderBottom = (isOuterBorder && isBottomRow) || isBottomRow ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderLeft = (isOuterBorder && isLeftColumn) ? BorderStyle.Thick : BorderStyle.Thin;
        style.BorderRight = (isOuterBorder && isRightColumn) ? BorderStyle.Thick : BorderStyle.Thin;

        return style;
    }

    private static void ApplyBordersToMergedRegion(ISheet sheet, IWorkbook workbook, CellRangeAddress mergedRegion)
    {
        for (int row = mergedRegion.FirstRow; row <= mergedRegion.LastRow; row++)
        {
            var sheetRow = sheet.GetRow(row);
            if (sheetRow != null)
            {
                for (int col = mergedRegion.FirstColumn; col <= mergedRegion.LastColumn; col++)
                {
                    var cell = sheetRow.GetCell(col);
                    if (cell != null)
                    {
                        var currentStyle = cell.CellStyle;
                        var newStyle = workbook.CreateCellStyle();
                        newStyle.CloneStyleFrom(currentStyle);

                        bool isTopRow = row == mergedRegion.FirstRow;
                        bool isBottomRow = row == mergedRegion.LastRow;
                        bool isLeftColumn = col == mergedRegion.FirstColumn;
                        bool isRightColumn = col == mergedRegion.LastColumn;

                        if (isTopRow && currentStyle.BorderTop != BorderStyle.None)
                        {
                            newStyle.BorderTop = currentStyle.BorderTop;
                            newStyle.TopBorderColor = currentStyle.TopBorderColor;
                        }
                        if (isBottomRow && currentStyle.BorderBottom != BorderStyle.None)
                        {
                            newStyle.BorderBottom = currentStyle.BorderBottom;
                            newStyle.BottomBorderColor = currentStyle.BottomBorderColor;
                        }
                        if (isLeftColumn && currentStyle.BorderLeft != BorderStyle.None)
                        {
                            newStyle.BorderLeft = currentStyle.BorderLeft;
                            newStyle.LeftBorderColor = currentStyle.LeftBorderColor;
                        }
                        if (isRightColumn && currentStyle.BorderRight != BorderStyle.None)
                        {
                            newStyle.BorderRight = currentStyle.BorderRight;
                            newStyle.RightBorderColor = currentStyle.RightBorderColor;
                        }

                        cell.CellStyle = newStyle;
                    }
                }
            }
        }
    }

    private class ExcelStyles
    {
        public ICellStyle TitleStyle { get; set; } = null!;
        public ICellStyle HeaderStyle { get; set; } = null!;
        public ICellStyle DataStyle { get; set; } = null!;
        public ICellStyle IntegerStyle { get; set; } = null!;
        public ICellStyle CalculatedStyle { get; set; } = null!;
        public ICellStyle CompensationStyle { get; set; } = null!;
        public ICellStyle TotalStyle { get; set; } = null!;
    }
}

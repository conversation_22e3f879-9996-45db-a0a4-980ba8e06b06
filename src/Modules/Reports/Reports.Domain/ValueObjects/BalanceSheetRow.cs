using Orion.SharedKernel.Domain.ValueObjects;

namespace Reports.Domain.ValueObjects;

public class BalanceSheetRow : ValueObject
{
    private const string MedellinAreaCode = "440405001";

    public string AreaCode { get; init; } = null!;
    public string AreaName { get; init; } = null!;
    public decimal UrbanCleaningTons { get; init; }
    public decimal SweepingTons { get; init; }
    public decimal NonRecyclableTons { get; init; }
    public decimal RejectionTons { get; init; }
    public decimal RecyclableTons { get; init; }
    public decimal TotalByNUAP { get; init; }
    public decimal Discounts { get; init; }

    public BalanceSheetRow(string areaCode, string areaName, HygieneTonsResume? recollection, WeighinTonsResume? finalDisposition)
    {
        AreaCode = areaCode;
        AreaName = areaName;
        UrbanCleaningTons = recollection?.UrbanCleaning ?? 0;
        SweepingTons = recollection?.Sweeping ?? 0;
        NonRecyclableTons = recollection?.NonRecyclable ?? 0;
        RejectionTons = recollection?.Rejection ?? 0;
        RecyclableTons = recollection?.Recyclable ?? 0;
        TotalByNUAP = areaCode == MedellinAreaCode && finalDisposition?.Emvarias > 0
            ? finalDisposition.Emvarias
            : finalDisposition?.Total ?? 0;
        Discounts = finalDisposition?.Discount ?? 0;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return AreaCode;
        yield return AreaName;
        yield return UrbanCleaningTons;
        yield return SweepingTons;
        yield return NonRecyclableTons;
        yield return RejectionTons;
        yield return RecyclableTons;
        yield return TotalByNUAP;
        yield return Discounts;
    }
}
